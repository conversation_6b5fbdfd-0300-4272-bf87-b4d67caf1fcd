{"doc": " 题目评论控制器\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "deleteComment", "paramTypes": ["java.lang.String"], "doc": " 删除题目评论\n\n @param commentId 评论ID\n @return 删除结果响应\n"}, {"name": "likeComment", "paramTypes": ["java.lang.String"], "doc": " 点赞/取消点赞评论\n\n @param commentId 评论ID\n @param request   HTTP请求对象\n @return 点赞结果响应\n"}, {"name": "reportComment", "paramTypes": ["java.lang.String", "java.util.Map"], "doc": " 举报评论\n\n @param commentId 评论ID\n @param request   举报请求参数\n @return 举报结果响应\n"}, {"name": "getCommentDetail", "paramTypes": ["java.lang.String"], "doc": " 获取评论详情\n\n @param commentId 评论ID\n @return 评论详情响应\n"}], "constructors": []}