2025-08-02 09:40:48 [main] ERROR o.s.b.c.p.m.PropertiesMigrationListener - 
The use of configuration keys that are no longer supported was found in the environment:

Property source 'Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' (document #10)':
	Key: xunfei.spark.api-key
		Line: 421
		Reason: none
	Key: xunfei.spark.api-secret
		Line: 422
		Reason: none

Property source 'Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' (document #19)':
	Key: xunfei.spark.api-key
		Line: 874
		Reason: none
	Key: xunfei.spark.api-secret
		Line: 875
		Reason: none


Please refer to the release notes or reference guide for potential alternatives.

2025-08-02 09:40:48 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [org.dromara.DromaraApplication]
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:194)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:418)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:290)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:349)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:118)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:791)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:609)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.dromara.DromaraApplication.main(DromaraApplication.java:22)
Caused by: org.springframework.context.annotation.ConflictingBeanDefinitionException: Annotation-specified bean name 'questionCommentController' for bean class [org.dromara.app.controller.learning.QuestionCommentController] conflicts with existing, non-compatible bean definition of same name and class [org.dromara.system.controller.question.QuestionCommentController]
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.checkCandidate(ClassPathBeanDefinitionScanner.java:361)
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.doScan(ClassPathBeanDefinitionScanner.java:288)
	at org.springframework.context.annotation.ComponentScanAnnotationParser.parse(ComponentScanAnnotationParser.java:128)
	at org.springframework.context.annotation.ConfigurationClassParser.doProcessConfigurationClass(ConfigurationClassParser.java:346)
	at org.springframework.context.annotation.ConfigurationClassParser.processConfigurationClass(ConfigurationClassParser.java:281)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:204)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:172)
	... 11 common frames omitted
2025-08-02 09:45:16 [main] ERROR o.s.b.c.p.m.PropertiesMigrationListener - 
The use of configuration keys that are no longer supported was found in the environment:

Property source 'Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' (document #10)':
	Key: xunfei.spark.api-key
		Line: 421
		Reason: none
	Key: xunfei.spark.api-secret
		Line: 422
		Reason: none

Property source 'Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' (document #19)':
	Key: xunfei.spark.api-key
		Line: 874
		Reason: none
	Key: xunfei.spark.api-secret
		Line: 875
		Reason: none


Please refer to the release notes or reference guide for potential alternatives.

2025-08-02 09:45:16 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.context.annotation.ConflictingBeanDefinitionException: Annotation-specified bean name 'questionCommentMapper' for bean class [org.dromara.system.mapper.QuestionCommentMapper] conflicts with existing, non-compatible bean definition of same name and class [org.mybatis.spring.mapper.MapperFactoryBean]
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.checkCandidate(ClassPathBeanDefinitionScanner.java:361)
	at org.mybatis.spring.mapper.ClassPathMapperScanner.checkCandidate(ClassPathMapperScanner.java:364)
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.doScan(ClassPathBeanDefinitionScanner.java:288)
	at org.mybatis.spring.mapper.ClassPathMapperScanner.doScan(ClassPathMapperScanner.java:255)
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.scan(ClassPathBeanDefinitionScanner.java:255)
	at org.mybatis.spring.mapper.MapperScannerConfigurer.postProcessBeanDefinitionRegistry(MapperScannerConfigurer.java:406)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:349)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:148)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:791)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:609)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.dromara.DromaraApplication.main(DromaraApplication.java:22)
